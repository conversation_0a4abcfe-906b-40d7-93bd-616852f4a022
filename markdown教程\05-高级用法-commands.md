# 命令行的使用

> **来源文件**: `advance\commands.html`  
> **转换时间**: 2025/08/14 周四  

---

* [](../.html)
  * 🧰 进阶使用
  * ⚙️ 命令行的使用

本页总览

# ⚙️ 命令行的使用

[](https://b23.tv/w62Tiqd)

DrissionPage 提供一些便捷的命令行命令，用于基本设置，以取代有时需要的临时配置文件。

命令行主命令为`dp`，形式为：
    
    
    dp 命令全称或缩写 <参数>
    

## ✅️️ 设置浏览器路径[​](. "✅️️ 设置浏览器路径的直接链接")

全称| 缩写| 参数| 说明  
---|---|---|---  
\--set-browser-path| -p| 浏览器路径| 设置配置文件中的浏览器路径  
  
**示例：**
    
    
    # 完整写法
    dp --set-browser-path "D:\chrome\Chrome.exe"
    
    # 简略写法
    dp -p "D:\chrome\Chrome.exe"
    

## ✅️️ 设置用户数据路径[​](. "✅️️ 设置用户数据路径的直接链接")

全称| 缩写| 参数| 说明  
---|---|---|---  
\--set-user-path| -u| 用户数据文件夹路径| 设置配置文件中的用户数据路径  
  
**示例：**
    
    
    # 完整写法
    dp --set-user-path D:\chrome\user_data
    
    # 简略写法
    dp -u D:\chrome\user_data
    

  


## ✅️️ 复制默认 ini 文件到当前路径[​](. "✅️️ 复制默认 ini 文件到当前路径的直接链接")

全称| 缩写| 参数| 说明  
---|---|---|---  
\--configs-to-here| -c| 无| 复制默认配置文件到当前路径  
  
**示例：**
    
    
    # 完整写法
    dp --configs-to-here
    
    # 简略写法
    dp -c
    

## ✅️️ 启动浏览器[​](. "✅️️ 启动浏览器的直接链接")

此命令用于启动浏览器，等待程序接管。

全称| 缩写| 参数| 说明  
---|---|---|---  
\--launch-browser| -l| 端口号| 启动浏览器，传入端口号，0表示用配置文件中的值  
  
**示例：**
    
    
    # 完整写法
    dp --launch-browser 9333
    
    # 简略写法
    dp -l 0
    

[上一页⚙️ 全局设置](settings.html)[下一页⚙️ 异常的使用](errors.html)

  * [✅️️ 设置浏览器路径](.)
  * [✅️️ 设置用户数据路径](.)
  * [✅️️ 复制默认 ini 文件到当前路径](.)
  * [✅️️ 启动浏览器](.)