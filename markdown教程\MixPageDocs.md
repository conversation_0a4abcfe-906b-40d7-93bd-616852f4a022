# 欢迎

> **来源文件**: `MixPageDocs.html`  
> **转换时间**: 2025/08/14 周四  

---

[ ](.html "MixPage") MixPage 

[ Gitee  ](https://gitee.com/g1879/MixPage "Go to repository")

  * 欢迎  [ 欢迎  ](.html) Table of contents 
    * [ 概述 ](.)
    * [ 背景 ](.)
  * 使用文档  使用文档 
    * [ 🔨 概述  ](https://drissionpage.cn/introduction/)
    * [ 🔨 创建页面对象  ](https://drissionpage.cn/create_page_object/)
    * [ 🔨 访问网页  ](https://drissionpage.cn/visit_web_page/)
    * [ 🔨 查找页面元素  ](https://drissionpage.cn/find_page_element/)
    * [ 🔨 获取元素信息  ](https://drissionpage.cn/get_element_info/)
    * [ 🔨 元素操作  ](https://drissionpage.cn/element_operation/)
    * [ 🔨 获取网页信息  ](https://drissionpage.cn/get_page_info/)
    * [ 🔨 页面操作  ](https://drissionpage.cn/page_operation/)
    * [ 🔨 cookies 的使用  ](https://drissionpage.cn/cookies/)
    * [ 🔨 Drission 对象  ](https://drissionpage.cn/Drission/)
    * [ 🔨 对接 selenium 及 requests 代码  ](https://drissionpage.cn/work_with_selenium_and_requests/)
    * [ 🔨 使用其它系统或浏览器  ](https://drissionpage.cn/use_other_browser/)
    * [ 🔨 DriverPage 和 SessionPage  ](https://drissionpage.cn/DriverPage_and_SessionPage/)

Table of contents 

  * [ 概述 ](.)
  * [ 背景 ](.)

# 欢迎

## 概述

MixPage 是一个基于 python 的网页自动化工具。

它对 selenium 和 requests 进行了封装，可实现两者的同步。

可兼顾浏览器自动化的便利性和 requests 的高效率。

它功能强大，内置无数人性化设计和便捷功能。

它的语法简洁而优雅，代码量少，对新手友好。

* * *

## 背景

MixPage 是 [DrissionPage](https://gitee.com/g1879/DrissionPage) 的早期版本，几年来做过大大小小几百个项目，已相当稳定。

但随着新版 DirssionPage 的发展，新版已对旧版形成全面的超越，旧版已到了退休的时候。

因此，将 MixPage 从 DrissionPage 上剥离，独立成为一个库。

以此来继续它的生命，和纪念它做出过的成果。

今后不会再有功能上的修改。

* * *

项目地址：[MixPage](https://gitee.com/g1879/MixPage)

文档地址：[MixPage 使用文档](http://g1879.gitee.io/mixpage)

DrissionPage 项目地址：[DrissionPage](https://gitee.com/g1879/DrissionPage)

**交流 QQ 群：** 897838127

Back to top