# 全局设置

> **来源文件**: `advance\settings.html`  
> **转换时间**: 2025/08/14 周四  

---

* [](../.html)
  * 🧰 进阶使用
  * ⚙️ 全局设置

本页总览

# ⚙️ 全局设置

[](https://b23.tv/w62Tiqd)

有一些运行时的全局设置，可以控制程序某些行为。

## ✅️️ 使用方式[​](. "✅️️ 使用方式的直接链接")

全局设置在`DrissionPage.common`路径中。

以`set_---()`的方式对属性进行设置。

设置方法会返回`Settings`类本身，所以支持链式操作。

使用方法：
    
    
    from DrissionPage.common import Settings
    
    Settings.set_raise_when_wait_failed(True)  # 设置等待失败时抛出异常
    Settings.set_language('en')  # 设置报错使用英文
    
    Settings.set_raise_when_wait_failed(True).set_auto_handle_alert(True)  # 链式操作
    

* * *

## ✅️️ 设置项[​](. "✅️️ 设置项的直接链接")

### 📌 `set_raise_when_ele_not_found()`[​](. "-set_raise_when_ele_not_found的直接链接")

设置找不到元素时，是否抛出异常。初始为`False`。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`on_off`| `bool`| `True`| `bool`表示开或关  
  
**返回：**`Settings`

* * *

### 📌 `set_raise_when_click_failed()`[​](. "-set_raise_when_click_failed的直接链接")

设置点击失败时，是否抛出异常。初始为`False`。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`on_off`| `bool`| `True`| `bool`表示开或关  
  
**返回：**`Settings`

* * *

### 📌 `set_raise_when_wait_failed()`[​](. "-set_raise_when_wait_failed的直接链接")

设置等待失败时，是否抛出异常。初始为`False`。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`on_off`| `bool`| `True`| `bool`表示开或关  
  
**返回：**`Settings`

* * *

### 📌 `set_singleton_tab_obj()`[​](. "-set_singleton_tab_obj的直接链接")

设置 Tab 对象是否使用单例模式。初始为`True`。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`on_off`| `bool`| `True`| `bool`表示开或关  
  
**返回：**`Settings`

* * *

### 📌 `set_cdp_timeout()`[​](. "-set_cdp_timeout的直接链接")

设置 cdp 执行超时（秒），初始为`30`。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`second`| `float`| 必填| 秒数  
  
**返回：**`Settings`

* * *

### 📌 `set_browser_connect_timeout()`[​](. "-set_browser_connect_timeout的直接链接")

设置连接浏览器的超时时间（秒）。初始为`30`。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`second`| `float`| 必填| 秒数  
  
**返回：**`Settings`

* * *

### 📌 `set_auto_handle_alert()`[​](. "-set_auto_handle_alert的直接链接")

全局的自动处理弹出设置。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`accept`| `bool`  
`None`| `True`| 为`None`时不自动处理，为`True`时自动接受，为`False`时自动取消。  
  
**返回：**`Settings`

* * *

### 📌 `set_language()`[​](. "-set_language的直接链接")

设置报错和提示信息语言。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`code`| `str`| 必填| 可选`'zh_cn'`、`'en'`  
  
**返回：**`Settings`

* * *

### 📌 `set_suffixes_list()`[​](. "-set_suffixes_list的直接链接")

设置用于解析域名后缀的本地文件路径。

默认会连网获取，离线环境下使用内置文件，可对此属性赋值手动指定路径。

通常离线环境下打包使用时需要设置。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`path`| `str`  
`Path`| 必填| 文件路径  
  
**返回：**`Settings`

* * *

  


## ✅️️ 示例[​](. "✅️️ 示例的直接链接")

此示例设置找不到元素时立刻抛出异常（如不设置返回`NoneElement`）。

可直接执行查看效果。
    
    
    from DrissionPage import SessionPage
    from DrissionPage.common import Settings
    
    Settings.set_raise_when_ele_not_found(True)
    
    page = SessionPage()
    page.get('https://www.baidu.com')
    ele = page('#abcd')
    

**输出：**
    
    
    ...前面省略...
    DrissionPage.errors.ElementNotFoundError: 
    没有找到元素。
    method: ele()
    args: {'locator': '#abcd'}
    

[上一页⚙️ 配置文件的使用](ini.html)[下一页⚙️ 命令行的使用](commands.html)

  * [✅️️ 使用方式](.)
  * [✅️️ 设置项](.)
    * [📌 `set_raise_when_ele_not_found()`](.)
    * [📌 `set_raise_when_click_failed()`](.)
    * [📌 `set_raise_when_wait_failed()`](.)
    * [📌 `set_singleton_tab_obj()`](.)
    * [📌 `set_cdp_timeout()`](.)
    * [📌 `set_browser_connect_timeout()`](.)
    * [📌 `set_auto_handle_alert()`](.)
    * [📌 `set_language()`](.)
    * [📌 `set_suffixes_list()`](.)
  * [✅️️ 示例](.)