# 安装

> **来源文件**: `get_start\installation.html`  
> **转换时间**: 2025/08/14 周四  

---

* [](../.html)
  * 🌏 安装

本页总览

# 🌏 安装

[](https://b23.tv/w62Tiqd)

## ✅️️ 运行环境[​](. "✅️️ 运行环境的直接链接")

操作系统：Windows、Linux 和 Mac

Python 版本：3.6 及以上

支持：Chromium 内核浏览器（如 Chrome 和 Edge）、electron 应用

* * *

## ✅️️ 安装[​](. "✅️️ 安装的直接链接")

请使用 pip 安装：
    
    
    pip install DrissionPage
    

* * *

## ✅️️ 升级[​](. "✅️️ 升级的直接链接")

### 📌 升级最新稳定版[​](. "📌 升级最新稳定版的直接链接")
    
    
    pip install DrissionPage --upgrade
    

* * *

### 📌 指定版本升级[​](. "📌 指定版本升级的直接链接")
    
    
    pip install DrissionPage==4.0.0b17
    

  


[下一页🌏 导入](https://drissionpage.cn/get_start/import)

  * [✅️️ 运行环境](.)
  * [✅️️ 安装](.)
  * [✅️️ 升级](.)
    * [📌 升级最新稳定版](.)
    * [📌 指定版本升级](.)