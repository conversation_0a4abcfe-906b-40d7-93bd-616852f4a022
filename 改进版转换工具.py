#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DrissionPage教程HTML转Markdown改进版工具
专门针对DrissionPage教程网页的结构进行优化
"""

import os
import re
import json
from pathlib import Path
from typing import List, Dict, Tuple, Optional

try:
    from bs4 import BeautifulSoup, NavigableString, Tag
    import html2text
except ImportError:
    print("正在安装必要的依赖包...")
    import subprocess
    import sys
    
    packages = ['beautifulsoup4', 'html2text', 'lxml']
    for package in packages:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
    
    from bs4 import BeautifulSoup, NavigableString, Tag
    import html2text


class DrissionPageConverter:
    """DrissionPage教程专用转换器"""
    
    def __init__(self, input_dir: str = "drissionpage教程网页/drissionpage.cn", 
                 output_dir: str = "markdown教程"):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 配置html2text
        self.h = html2text.HTML2Text()
        self.h.ignore_links = False
        self.h.ignore_images = False
        self.h.ignore_emphasis = False
        self.h.body_width = 0  # 不限制行宽
        self.h.unicode_snob = True
        self.h.escape_snob = False
        
        # 章节映射表（更详细）
        self.chapter_mapping = {
            'get_start': '01-快速开始',
            'browser_control': '02-浏览器控制',
            'download': '03-下载功能', 
            'features': '04-特色功能',
            'advance': '05-高级用法',
            'tutorials': '06-实战教程',
            'SessionPage': '07-SessionPage',
            'versions': '08-版本信息'
        }
        
        # 子章节映射（更完整）
        self.sub_chapter_mapping = {
            'installation': '01-安装配置',
            'intro': '01-介绍',
            'browser_object': '02-浏览器对象',
            'browser_options': '03-浏览器选项',
            'connect_browser': '04-连接浏览器',
            'get_elements': '05-获取元素',
            'ele_operation': '06-元素操作',
            'page_operation': '07-页面操作',
            'actions': '08-动作操作',
            'waiting': '09-等待机制',
            'tabs': '10-标签页管理',
            'iframe': '11-iframe处理',
            'upload': '12-文件上传',
            'visit': '13-页面访问',
            'console': '14-控制台',
            'listener': '15-监听器',
            'screen': '16-屏幕操作',
            'mode_change': '17-模式切换',
            'pages': '18-页面管理',
            'get_page_info': '19-获取页面信息',
            'get_ele_info': '20-获取元素信息',
            'behavior': '21-行为模式',
            'filter': '22-筛选功能',
            'find_in_object': '23-对象内查找',
            'relative': '24-相对定位',
            'sheet': '25-语法速查',
            'simplify': '26-简化写法',
            'syntax': '27-定位语法'
        }

    def extract_clean_content(self, soup: BeautifulSoup) -> str:
        """提取并清理HTML内容"""
        # 移除所有script和style标签
        for tag in soup(['script', 'style', 'meta', 'link', 'noscript']):
            tag.decompose()
        
        # 移除base64图片
        for img in soup.find_all('img'):
            src = img.get('src', '')
            if src.startswith('data:image'):
                img.decompose()
        
        # 查找主要内容
        # 尝试多种可能的内容容器
        content_candidates = [
            soup.find('main'),
            soup.find('article'),
            soup.find('div', class_=lambda x: x and 'content' in str(x).lower()),
            soup.find('div', class_=lambda x: x and 'main' in str(x).lower()),
            soup.find('div', id=lambda x: x and 'content' in str(x).lower()),
            soup.find('body')
        ]
        
        main_content = None
        for candidate in content_candidates:
            if candidate and candidate.get_text().strip():
                main_content = candidate
                break
        
        if not main_content:
            main_content = soup
        
        # 使用html2text转换
        html_str = str(main_content)
        markdown_content = self.h.handle(html_str)
        
        return markdown_content

    def clean_markdown_content(self, content: str) -> str:
        """清理转换后的Markdown内容"""
        # 移除base64图片数据
        content = re.sub(r'!\[.*?\]\(data:image/[^)]+\)', '[图片]', content)
        
        # 清理多余的空行
        content = re.sub(r'\n{4,}', '\n\n', content)
        
        # 移除HTML注释残留
        content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
        
        # 清理多余的星号和下划线
        content = re.sub(r'\*{3,}', '---', content)
        content = re.sub(r'_{3,}', '---', content)
        
        # 修复代码块
        content = re.sub(r'```\s*\n\s*```', '', content)  # 移除空代码块
        content = re.sub(r'```(\w+)?\n\s*\n', r'```\1\n', content)  # 清理代码块开头
        
        # 为没有语言标识的代码块添加python标识
        content = re.sub(r'```\n(?=[a-zA-Z_])', '```python\n', content)
        
        # 清理表格格式
        content = re.sub(r'\|\s*\|\s*\|', '| |', content)
        
        return content.strip()

    def extract_title_from_content(self, content: str, file_path: Path) -> str:
        """从内容中提取标题"""
        # 查找第一个一级标题
        title_match = re.search(r'^#\s+(.+)$', content, re.MULTILINE)
        if title_match:
            title = title_match.group(1).strip()
            # 清理emoji和特殊字符
            title = re.sub(r'[🌏⤵️📖🎯🔧📚📄🛰️🔦💥⚙️🛩️]', '', title).strip()
            return title
        
        # 如果没找到，使用文件名生成
        return self.generate_title_from_path(file_path)

    def generate_title_from_path(self, file_path: Path) -> str:
        """根据文件路径生成标题"""
        parts = file_path.parts
        filename = file_path.stem
        
        # 查找主章节
        main_chapter = ""
        for part in parts:
            if part in self.chapter_mapping:
                main_chapter = self.chapter_mapping[part]
                break
        
        # 查找子章节
        sub_chapter = self.sub_chapter_mapping.get(filename, filename.replace('_', ' ').title())
        
        if main_chapter:
            return f"{main_chapter} - {sub_chapter}"
        else:
            return sub_chapter

    def generate_filename(self, file_path: Path) -> str:
        """根据文件路径生成中文文件名"""
        parts = file_path.parts
        filename = file_path.stem
        
        # 获取主章节
        main_chapter = ""
        for part in parts:
            if part in self.chapter_mapping:
                main_chapter = self.chapter_mapping[part]
                break
        
        # 获取子章节
        sub_chapter = self.sub_chapter_mapping.get(filename, filename.replace('_', '-'))
        
        if main_chapter and sub_chapter:
            return f"{main_chapter}-{sub_chapter}.md"
        elif main_chapter:
            return f"{main_chapter}.md"
        else:
            return f"{sub_chapter}.md"

    def convert_single_file(self, html_file: Path) -> Optional[Tuple[str, str]]:
        """转换单个HTML文件"""
        try:
            print(f"正在处理: {html_file.relative_to(self.input_dir)}")
            
            # 读取HTML文件
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 提取并清理内容
            markdown_content = self.extract_clean_content(soup)
            
            # 后处理Markdown内容
            markdown_content = self.clean_markdown_content(markdown_content)
            
            # 如果内容太少，跳过
            if len(markdown_content.strip()) < 100:
                print(f"⚠️  内容太少，跳过: {html_file.name}")
                return None
            
            # 提取标题
            title = self.extract_title_from_content(markdown_content, html_file)
            
            # 构建最终内容
            final_content = f"# {title}\n\n"
            final_content += f"> **来源文件**: `{html_file.relative_to(self.input_dir)}`  \n"
            final_content += f"> **转换时间**: {os.popen('date /t').read().strip()}  \n\n"
            final_content += "---\n\n"
            final_content += markdown_content
            
            return title, final_content
            
        except Exception as e:
            print(f"❌ 处理文件 {html_file.name} 时出错: {e}")
            return None

    def scan_html_files(self) -> List[Path]:
        """扫描所有HTML文件"""
        html_files = []
        for html_file in self.input_dir.rglob("*.html"):
            # 跳过索引文件和支持页面
            if html_file.name in ['index.html', 'support.html']:
                continue
            html_files.append(html_file)
        
        # 按路径排序，确保章节顺序
        html_files.sort(key=lambda x: (str(x.parent), x.name))
        return html_files

    def convert_all_files(self):
        """转换所有HTML文件"""
        html_files = self.scan_html_files()
        
        if not html_files:
            print("❌ 未找到HTML文件！")
            return
        
        print(f"📁 找到 {len(html_files)} 个HTML文件")
        print("=" * 50)
        
        converted_files = []
        failed_files = []
        
        for i, html_file in enumerate(html_files, 1):
            print(f"[{i:2d}/{len(html_files)}] ", end="")
            
            result = self.convert_single_file(html_file)
            if result:
                title, content = result
                
                # 生成输出文件名
                output_filename = self.generate_filename(html_file)
                output_path = self.output_dir / output_filename
                
                # 确保输出目录存在
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 写入文件
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                converted_files.append((html_file, output_path, title))
                print(f"✅ 已转换: {output_filename}")
            else:
                failed_files.append(html_file)
        
        print("=" * 50)
        
        # 生成转换报告和目录
        self.generate_conversion_report(converted_files, failed_files)
        self.generate_table_of_contents(converted_files)
        
        print(f"\n📊 转换完成！")
        print(f"✅ 成功转换: {len(converted_files)} 个文件")
        print(f"❌ 转换失败: {len(failed_files)} 个文件")
        print(f"📂 输出目录: {self.output_dir}")

    def generate_table_of_contents(self, converted_files: List):
        """生成目录索引"""
        toc_content = "# DrissionPage 教程目录\n\n"
        toc_content += f"📅 生成时间: {os.popen('date /t').read().strip()}\n\n"
        
        # 按章节分组
        chapters = {}
        for html_file, md_file, title in converted_files:
            # 从文件名提取章节信息
            filename = md_file.name
            if '-' in filename:
                chapter = filename.split('-')[0]
                if chapter not in chapters:
                    chapters[chapter] = []
                chapters[chapter].append((md_file.name, title))
        
        # 生成目录
        for chapter in sorted(chapters.keys()):
            chapter_name = chapter.replace('01', '第一章').replace('02', '第二章').replace('03', '第三章')\
                          .replace('04', '第四章').replace('05', '第五章').replace('06', '第六章')\
                          .replace('07', '第七章').replace('08', '附录')
            
            toc_content += f"## {chapter_name}\n\n"
            
            for filename, title in sorted(chapters[chapter]):
                toc_content += f"- [{title}]({filename})\n"
            
            toc_content += "\n"
        
        # 写入目录文件
        toc_path = self.output_dir / "README.md"
        with open(toc_path, 'w', encoding='utf-8') as f:
            f.write(toc_content)
        
        print(f"📋 已生成目录文件: README.md")

    def generate_conversion_report(self, converted_files: List, failed_files: List):
        """生成详细的转换报告"""
        report_content = "# 转换报告\n\n"
        report_content += f"🕒 转换时间: {os.popen('date /t').read().strip()}\n"
        report_content += f"📊 总文件数: {len(converted_files) + len(failed_files)}\n"
        report_content += f"✅ 成功转换: {len(converted_files)}\n"
        report_content += f"❌ 转换失败: {len(failed_files)}\n\n"
        
        if converted_files:
            report_content += "## ✅ 成功转换的文件\n\n"
            report_content += "| 序号 | 标题 | 源文件 | 输出文件 |\n"
            report_content += "|------|------|--------|----------|\n"
            
            for i, (html_file, md_file, title) in enumerate(converted_files, 1):
                source_path = html_file.relative_to(self.input_dir)
                report_content += f"| {i:2d} | {title} | `{source_path}` | `{md_file.name}` |\n"
        
        if failed_files:
            report_content += "\n## ❌ 转换失败的文件\n\n"
            for i, html_file in enumerate(failed_files, 1):
                source_path = html_file.relative_to(self.input_dir)
                report_content += f"{i}. `{source_path}`\n"
        
        # 写入报告文件
        report_path = self.output_dir / "转换报告.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)


def main():
    """主函数"""
    print("🚀 DrissionPage HTML转Markdown 改进版工具")
    print("=" * 50)
    
    # 检查输入目录是否存在
    input_dir = "drissionpage教程网页/drissionpage.cn"
    if not Path(input_dir).exists():
        print(f"❌ 错误: 输入目录 '{input_dir}' 不存在！")
        print("请确保HTML文件位于正确的目录中。")
        return
    
    # 创建转换器实例
    converter = DrissionPageConverter()
    
    # 执行转换
    try:
        converter.convert_all_files()
        print("\n🎉 转换任务完成！")
        print(f"📂 请查看输出目录: {converter.output_dir}")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断了转换过程。")
    except Exception as e:
        print(f"❌ 转换过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
