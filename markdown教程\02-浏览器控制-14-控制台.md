# 获取控制台信息

> **来源文件**: `browser_control\console.html`  
> **转换时间**: 2025/08/14 周四  

---

* [](../.html)
  * 🚀 控制浏览器
  * 🛰️ 获取控制台信息

本页总览

# 🛰️ 获取控制台信息

[](https://b23.tv/w62Tiqd)

获取控制台信息的逻辑和监听网络数据差不多，是通过监听控制台数据实现的。

注意

不是所有显示在控制台的信息都能获取，需要用`console.log()`等方法输出到控制台的才能获取。

## ✅️ 示例[​](. "✅️ 示例的直接链接")
    
    
    from DrissionPage import Chromium
    
    tab = Chromium().latest_tab
    tab.console.start()
    tab.run_js('console.log("DrissionPage");')
    data = tab.console.wait()
    print(data.text)  # 输出：DrissionPage
    

* * *

## ✅️ 启动和停止[​](. "✅️ 启动和停止的直接链接")

### 📌 `console.start()`[​](. "-consolestart的直接链接")

此方法用于启动控制台信息监听。

**参数：** 无

**返回：**`None`

* * *

### 📌 `console.stop()`[​](. "-consolestop��的直接链接")

此方法用于停止监听，清空已监听到的信息列表。

**参数：** 无

**返回：**`None`

* * *

  


## ✅️ 获取信息[​](. "✅️ 获取信息的直接链接")

### 📌 `console.wait()`[​](. "-consolewait的直接链接")

此方法用于等待一条控制台信息。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`timeout`| `float`  
`None`| `None`| 超时时间（秒），为`None`无限等待  
返回类型| 说明  
---|---  
`ConsoleData`| 控制台信息数据包对象  
`False`| 等待超时时  
  
* * *

### 📌 `console.steps()`[​](. "-consolesteps的直接链接")

此方法返回一个可迭代对象，用于`for`循环，每次循环可从中获取到的信息。

可实现实时获取并返回数据包。

如果`timeout`超时，会中断循环。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`timeout`| `float`  
`None`| `None`| 每个信息等待时间（秒），为`None`表示无限等待  
返回类型| 说明  
---|---  
`ConsoleData`| 控制台信息数据包对象  
  
* * *

### 📌 `console.messages`[​](. "-consolemessages的直接链接")

此属性以`list`方式返回获取到的信息，返回后会清空列表。

返回类型| 说明  
---|---  
`List[ConsoleData]`| 控制台信息对象组成的列表  
  
* * *

## ✅️ 其它[​](. "✅️ 其它的直接链接")

### 📌 `console.listening`[​](. "-consolelistening的直接链接")

此属性返回监听是否进行中。

**返回：** `bool`

* * *

### 📌 `console.clear()`[​](. "-consoleclear的直接��链接")

此方法用于清空已获取但未返回的信息。

**参数：** 无

**返回：**`None`

* * *

## ✅️ `ConsoleData`对象[​](. "️-consoledata对象的直接链接")

`ConsoleData`对象是获取到的数据包结果对象，包含了数据包各种信息。

### 📌 `对象属性`[​](. "-对象属性的直接链接")

属性名称| 数据类型| 说明  
---|---|---  
`source`| `str`| 来源  
`level`| `str`| 类型  
`text`| `str`| 内容文本  
`body`| `Any`| 把`text`进行 json 解析  
`url`| `str`| 网址  
`line`| `str`| 行号  
`column`| `str`| 列号  
  
[上一页🛰️ 监听网络数据](listener.html)[下一页🛰️ 截图和录像](screen.html)

  * [✅️ 示例](.)
  * [✅️ 启动和停止](.)
    * [📌 `console.start()`](.)
    * [📌 `console.stop()`](.)
  * [✅️ 获取信息](.)
    * [📌 `console.wait()`](.)
    * [📌 `console.steps()`](.)
    * [📌 `console.messages`](.)
  * [✅️ 其它](.)
    * [📌 `console.listening`](.)
    * [📌 `console.clear()`](.)
  * [✅️ `ConsoleData`对象](.)
    * [📌 `对象属性`](.)