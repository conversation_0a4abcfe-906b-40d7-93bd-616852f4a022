#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DrissionPage完整认证API请求示例：获取真实认证信息后发起请求
功能：先监听真实的API请求获取认证信息，然后复制这些信息发起新请求
"""

from DrissionPage import Chromium
import json
import time
import os
from datetime import datetime

def save_to_json_file(data, filename_prefix="api_response"):
    """
    将数据保存到JSON文件
    """
    # 创建保存目录
    save_dir = "API响应数据"
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
        print(f"📁 创建保存目录：{save_dir}")

    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{filename_prefix}_{timestamp}.json"
    filepath = os.path.join(save_dir, filename)

    try:
        # 保存数据到JSON文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)

        print(f"💾 数据已保存到文件：{filepath}")
        return filepath
    except Exception as e:
        print(f"❌ 保存文件失败：{e}")
        return None

def monitor_and_copy_real_request():
    """
    监听真实的API请求，获取完整的认证信息
    """
    print("🚀 开始监听真实API请求以获取认证信息...")
    
    # 连接到9222端口的浏览器
    browser = Chromium(9222)
    print("✅ 已连接到浏览器")
    
    # 新建标签页
    new_tab = browser.new_tab()
    print("📄 新建空白标签页完成")
    
    # 激活标签页
    new_tab.set.activate()
    print("🎯 标签页已激活")
    
    # 启动网络监听器 - 监听目标API
    target_api = "visage-agent-seller/product/skc/pageQuery"
    print("🔍 启动网络监听器...")
    print(f"🎯 目标API: {target_api}")
    new_tab.listen.start(targets=target_api)
    print("✅ 网络监听器已启动")
    
    # 访问目标页面
    target_url = "https://agentseller.temu.com/goods/list"
    print(f"🌐 正在访问：{target_url}")
    
    # 访问页面
    new_tab.get(target_url)
    
    print("⏳ 等待页面加载...")
    new_tab.wait.load_start()
    new_tab.wait.doc_loaded()
    print("✅ 页面加载完成")
    
    # 等待并捕获真实的API请求
    print("\n⏳ 等待真实的API请求...")
    try:
        packet = new_tab.listen.wait(timeout=30)
        if packet:
            print("✅ 成功捕获到真实的API请求！")
            return new_tab, packet
        else:
            print("⚠️ 未捕获到API请求")
            return new_tab, None
    except Exception as e:
        print(f"❌ 监听API请求时出错：{e}")
        return new_tab, None

def extract_auth_info(packet):
    """
    从真实请求中提取认证信息并保存到文件
    """
    print("\n" + "=" * 80)
    print("🔍 提取真实请求的认证信息")
    print("=" * 80)

    if not packet or not packet.request:
        print("❌ 无法获取请求信息")
        return None

    # 提取重要的认证头
    auth_headers = {}
    important_headers = [
        'Anti-Content', 'mallid', 'cookie', 'authorization',
        'x-csrf-token', 'x-requested-with', 'sec-ch-ua',
        'sec-ch-ua-mobile', 'sec-ch-ua-platform', 'user-agent',
        'accept', 'accept-language', 'cache-control', 'content-type',
        'origin', 'referer', 'sec-fetch-dest', 'sec-fetch-mode', 'sec-fetch-site'
    ]

    print("📋 正在提取认证头信息...")
    for header_name in important_headers:
        for key, value in packet.request.headers.items():
            if key.lower() == header_name.lower():
                auth_headers[key] = value
                break

    # 提取POST数据
    post_data = None
    if packet.request.postData:
        try:
            if isinstance(packet.request.postData, dict):
                post_data = packet.request.postData
            else:
                post_data = json.loads(str(packet.request.postData))
            print("📝 已提取POST数据")
        except:
            post_data = str(packet.request.postData)
            print("📝 已提取POST数据（原始格式）")

    # 构建认证信息数据
    auth_info = {
        'extraction_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'url': packet.url,
        'method': packet.request.method if hasattr(packet.request, 'method') else 'POST',
        'headers': auth_headers,
        'post_data': post_data,
        'description': '从真实API请求中提取的认证信息'
    }

    # 保存认证信息到文件
    save_to_json_file(auth_info, "认证信息")
    print("✅ 认证信息已保存到JSON文件")

    return {
        'headers': auth_headers,
        'post_data': post_data,
        'url': packet.url
    }

def send_custom_api_request(tab, auth_info):
    """
    使用提取的认证信息发起自定义API请求
    """
    print("\n" + "=" * 80)
    print("📡 使用提取的认证信息发起自定义API请求")
    print("=" * 80)
    
    if not auth_info:
        print("❌ 没有认证信息，无法发起请求")
        return None
    
    # 自定义的请求数据
    custom_request_data = {
        "jitStockQuantitySection": {
            "leftValue": 0,
            "rightValue": 0
        },
        "skcJitStatus": 1,
        "page": 1,
        "pageSize": 20
    }
    
    api_url = auth_info['url']
    headers = auth_info['headers']
    
    print(f"🎯 API地址：{api_url}")
    print(f"📋 请求方法：POST")
    print(f"📝 自定义请求数据：")
    print(json.dumps(custom_request_data, indent=4, ensure_ascii=False))
    
    try:
        print("\n⏳ 正在发起自定义API请求...")
        
        # 使用JavaScript在浏览器中发起请求，这样可以保持完整的浏览器上下文
        js_code = f'''
        fetch('{api_url}', {{
            method: 'POST',
            headers: {json.dumps(headers)},
            body: JSON.stringify({json.dumps(custom_request_data)})
        }})
        .then(response => {{
            return response.text().then(text => {{
                return {{
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: text
                }};
            }});
        }})
        .then(data => {{
            window.customApiResponse = data;
            return data;
        }})
        .catch(error => {{
            window.customApiResponse = {{
                error: error.message
            }};
            return window.customApiResponse;
        }});
        '''
        
        # 执行JavaScript代码
        tab.run_js(js_code)
        
        # 等待请求完成
        print("⏳ 等待API请求完成...")
        time.sleep(3)
        
        # 获取响应结果
        response_data = tab.run_js('return window.customApiResponse;')
        
        if response_data:
            print("✅ 自定义API请求发送成功！")

            # 检查是否有错误
            if 'error' in response_data:
                print(f"❌ 请求出错：{response_data['error']}")
                # 保存错误信息
                error_data = {
                    'request_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'api_url': api_url,
                    'request_data': custom_request_data,
                    'error': response_data['error'],
                    'description': 'API请求错误信息'
                }
                save_to_json_file(error_data, "API请求错误")
                return None

            print(f"📥 响应状态码：{response_data.get('status', 'Unknown')}")
            print(f"📄 响应状态文本：{response_data.get('statusText', 'Unknown')}")

            # 构建完整的响应数据
            complete_response_data = {
                'request_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'api_url': api_url,
                'request_method': 'POST',
                'request_headers': headers,
                'request_data': custom_request_data,
                'response_status': response_data.get('status', 'Unknown'),
                'response_status_text': response_data.get('statusText', 'Unknown'),
                'response_headers': response_data.get('headers', {}),
                'response_body': None,
                'description': '自定义API请求的完整响应信息'
            }

            # 处理响应内容
            try:
                # 尝试解析JSON响应
                response_json = json.loads(response_data['body'])
                complete_response_data['response_body'] = response_json
                print("📄 响应内容已解析为JSON格式")
            except Exception as e:
                complete_response_data['response_body'] = response_data['body']
                complete_response_data['json_parse_error'] = str(e)
                print(f"⚠️ JSON解析失败，保存原始响应内容")

            # 保存完整响应数据到文件
            save_to_json_file(complete_response_data, "API完整响应")
            print("💾 完整响应信息已保存到JSON文件")

            return response_data
        else:
            print("❌ 未获取到响应数据")
            return None
            
    except Exception as e:
        print(f"❌ API请求失败：{e}")
        return None

if __name__ == "__main__":
    """
    主程序：监听真实请求，提取认证信息，发起自定义请求
    """
    print("=" * 80)
    print("📚 DrissionPage完整认证API请求学习示例")
    print("🎯 目标：复制真实请求的认证信息发起自定义请求")
    print("=" * 80)
    
    try:
        # 步骤1：监听真实的API请求
        print("🚀 第一步：监听真实的API请求")
        tab, real_packet = monitor_and_copy_real_request()
        
        if tab and real_packet:
            # 步骤2：提取认证信息
            print("\n🚀 第二步：提取认证信息")
            auth_info = extract_auth_info(real_packet)
            
            if auth_info:
                # 步骤3：发起自定义API请求
                print("\n🚀 第三步：发起自定义API请求")
                custom_response = send_custom_api_request(tab, auth_info)
                
                if custom_response and custom_response.get('status') == 200:
                    print("\n🎉 自定义API请求成功！")
                    print("📋 成功使用真实的认证信息发起了自定义请求")
                    print("💾 所有响应数据已保存到JSON文件中")
                else:
                    print("\n⚠️ 自定义API请求未成功")
                    print("💡 可能需要进一步调试认证机制")
                    print("💾 错误信息已保存到JSON文件中")
            else:
                print("\n❌ 无法提取认证信息")
        else:
            print("\n❌ 未能监听到真实的API请求")
            print("💡 建议：")
            print("   1. 确保页面会自动发起API请求")
            print("   2. 尝试手动刷新页面或进行操作")
            print("   3. 检查API路径是否正确")
        
        # 停止监听器
        if tab:
            tab.listen.stop()
            print("\n✅ 网络监听器已停止")
        
        print("\n📋 操作总结：")
        print("   ✅ 尝试监听真实的API请求")
        print("   ✅ 提取了完整的认证信息并保存到JSON文件")
        print("   ✅ 使用认证信息发起自定义请求")
        print("   ✅ 将所有响应数据保存到JSON文件")
        print("   📁 所有数据文件保存在：API响应数据/ 目录下")
        
    except Exception as e:
        print(f"❌ 程序执行过程中发生错误：{e}")
        print("💡 请检查：")
        print("   1. 浏览器是否正确安装并可以启动")
        print("   2. 网络连接是否正常")
        print("   3. 目标网址是否可访问")
        print("   4. DrissionPage库是否正确安装")
    
    print("\n" + "=" * 80)
    print("📚 DrissionPage完整认证API请求学习完成")
    print("💡 这种方法可以复制任何真实请求的认证信息")
    print("💾 所有数据已保存到JSON文件，便于后续分析和使用")
    print("📁 文件保存位置：API响应数据/ 目录")
    print("=" * 80)
