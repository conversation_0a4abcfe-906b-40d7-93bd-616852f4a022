# 异常的使用

> **来源文件**: `advance\errors.html`  
> **转换时间**: 2025/08/14 周四  

---

* [](../.html)
  * 🧰 进阶使用
  * ⚙️ 异常的使用

本页总览

# ⚙️ 异常的使用

[](https://b23.tv/w62Tiqd)

本节介绍 DrissionPage 中的自定义异常。

## ✅️️ 导入[​](. "✅️️ 导入的直接链接")

各种异常放在`DrissionPage.errors`路径中。
    
    
    from DrissionPage.errors import *
    

## ✅️️ 异常介绍[​](. "✅️️ 异常介绍的直接链接")

### 📌 `ElementNotFoundError`[​](. "-elementnotfounderror的直接链接")

找不到元素时抛出。

* * *

### 📌 `AlertExistsError`[​](. "-alertexistserror的直接链接")

执行 JS 或调用通过 JS 实现的功能时，若存在未处理的弹出框则抛出。

* * *

### 📌 `ContextLostError`[​](. "-contextlosterror的直接链接")

页面被刷新后仍调用其中的元素时抛出。

* * *

### 📌 `ElementLostError`[​](. "-elementlosterror的直接链接")

元素因页面或自身被刷新而失效后，仍对其进行调用时抛出。

* * *

### 📌 `CDPError`[​](. "-cdperror的直接链接")

调用 cdp 方法产生异常时抛出。

* * *

### 📌 `PageDisconnectedError`[​](. "-pagedisconnectederror的直接链接")

页面关闭或连接断开后仍调用其功能时抛出。

* * *

### 📌 `JavaScriptError`[​](. "-javascripterror的直接链接")

JavaScript 运行错误时抛出。

* * *

### 📌 `NoRectError`[​](. "-norecterror的直接链接")

对没有大小和位置信息的元素获取这些信息时抛出。

* * *

### 📌 `BrowserConnectError`[​](. "-browserconnecterror的直接链接")

连接浏览器出错时抛出。

* * *

### 📌 `NoResourceError`[​](. "-noresourceerror的直接链接")

浏览器元素`src()`和`save()`获取资源失败时抛出。

* * *

### 📌 `CanNotClickError`[​](. "-cannotclickerror的直接链接")

* * *

点击元素时如元素不可点击，且设置允许抛出时抛出。

### 📌 `GetDocumentError`[​](. "-getdocumenterror的直接链接")

获取页面文档失败时抛出

* * *

获取页面文档失败时抛出。

### 📌 `WaitTimeoutError`[​](. "-waittimeouterror的直接链接")

自动等待失败，且设置允许抛出时抛出。

* * *

### 📌 `IncorrectURLError`[​](. "-incorrecturlerror的直接链接")

访问格式不正确的 url 时抛出。

* * *

### 📌 `StorageError`[​](. "-storageerror的直接链接")

操作数据时，如网站禁止操作则抛出。

* * *

### 📌 `CookieFormatError`[​](. "-cookieformaterror的直接链接")

导入 cookie 时如格式不正确则抛出。

* * *

### 📌 `LocatorError`[​](. "-locatorerror的直接链接")

传入的定位符格式不正确时抛出。

* * *

### 📌 `UnknownError`[​](. "-unknownerror的直接链接")

发生未知错误时抛出。

  


[上一页⚙️ 命令行的使用](commands.html)[下一页⚙️ 数据读取加速](accelerate.html)

  * [✅️️ 导入](.)
  * [✅️️ 异常介绍](.)
    * [📌 `ElementNotFoundError`](.)
    * [📌 `AlertExistsError`](.)
    * [📌 `ContextLostError`](.)
    * [📌 `ElementLostError`](.)
    * [📌 `CDPError`](.)
    * [📌 `PageDisconnectedError`](.)
    * [📌 `JavaScriptError`](.)
    * [📌 `NoRectError`](.)
    * [📌 `BrowserConnectError`](.)
    * [📌 `NoResourceError`](.)
    * [📌 `CanNotClickError`](.)
    * [📌 `GetDocumentError`](.)
    * [📌 `WaitTimeoutError`](.)
    * [📌 `IncorrectURLError`](.)
    * [📌 `StorageError`](.)
    * [📌 `CookieFormatError`](.)
    * [📌 `LocatorError`](.)
    * [📌 `UnknownError`](.)