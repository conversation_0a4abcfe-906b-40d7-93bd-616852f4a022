# 页面设置

> **来源文件**: `SessionPage\settings.html`  
> **转换时间**: 2025/08/14 周四  

---

* [](../.html)
  * 🛫 SessionPage
  * 🛩️ 页面设置

本页总览

# 🛩️ 页面设置

[](https://b23.tv/w62Tiqd)

本节介绍`SessionPage`运行参数设置。

这些设置是全局参数，设置后每次请求都会使用它们。

**示例：**
    
    
    from DrissionPage import SessionPage
    
    page = SessionPage()
    page.set.cookies([{'name': 'a', 'value': '1'}, {'name': 'b', 'value': '2'}])
    

## ✅️️ `set.retry_times()`[​](. "️️-setretry_times的直接链接")

此方法用于设置连接失败时重连次数。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`times`| `int`| 必填| 次数  
  
**返回：**`None`

## ✅️️ `set.retry_interval()`[​](. "️️-setretry_interval的直接链接")

此方法用于设置连接失败时重连间隔。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`interval`| `float`| 必填| 秒数  
  
**返回：**`None`

## ✅️️ `set.timeout()`[​](. "️️-settimeout的直接链接")

此方法用于设置连接超时时间（秒）。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`second`| `float`| 必填| 秒数  
  
**返回：**`None`

**示例：**
    
    
    page.set.timeout(20)
    

* * *

## ✅️️ `set.encoding()`[​](. "️️-setencoding的直接链接")

此方法用于设置网页编码。

默认情况下，程序会自动从 headers、页面上获取编码，但总有些奇葩网页的编码不准确。这时候可以主动设置编码。

可以针对已获取的`Rsponse`对象设置，或作为整体设置对之后的连接都有效。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`encoding`| `str`| 必填| 编码名称，如果要取消之前的设置，传入`None`  
`set_all`| `bool`| `True`| 是否设置对象参数，为`False`则只设置当前`Response`对象  
  
**返回：**`None`

* * *

## ✅️️ `set.cookies()`[​](. "️️-setcookies的直接链接")

此方法用于设置一个或多个 cookie。

设置一个 cookie 支持的格式：

  * `Cookie`：单个`Cookie`对象
  * `str`：`'name=value; domain=---; ...'`或`'name=---; value=---; domain=---; ...'`格式，只支持用`';'`分隔
  * `dict`：`{'name': '---', 'value': '---', 'domain': '---', ...}`或`{name: value, 'domain': '---', ...}`格式

设置多个 cookie 支持的格式：

  * `list`或`tuple`：上面几种形式的单个 cookie 放到列表中传入即可
  * `dict`：`{name1: value1, name2: value2, ..., 'domain': '---', ...}`格式
  * `str`：`'name1=value1; name2=value2; ... domain=---; ...'`格式，多个 cookie 之间只能用`';'`分隔
  * `CookieJar`：单个`CookieJar`对象

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`cookies`| `Cookie`  
`CookieJar`  
`list`  
`tuple`  
`str`  
`dict`| 必填| cookies 信息  
  
**返回：**`None`

* * *

  


## ✅️️ `set.cookies.clear()`[​](. "️️-setcookiesclear的直接链接")

此方法用于清除所有 cookie。

**参数：** 无

**返回：**`None`

* * *

## ✅️️ `set.cookies.remove()`[​](. "️️-setcookiesremove的直接链接")

此方法用于删除一个 cookie。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`name`| `str`| 必填| cookie 的 name 字段  
  
**返回：**`None`

* * *

## ✅️️ `set.headers()`[​](. "️️-setheaders的直接链接")

此方法用于设置 headers，会取代已有 headers。

headers 可以是`dict`格式的，也可以是文本格式。

文本格式不同字段用`\n`分隔，字段 key 和 value 用`': '`分隔，即从浏览器直接复制的格式。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`headers`| `dict`  
`str`| 必填| headers 信息  
  
**返回：**`None`

* * *

## ✅️️ `set.header()`[​](. "️️-setheader的直接链接")

此方法用于设置 headers 中一个项。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`name`| `str`| 必填| 设置名称  
`value`| `str`| 必填| 设置值  
  
**返回：**`None`

* * *

## ✅️️ `set.user_agent()`[​](. "️️-setuser_agent的直接链接")

此方法用于设置 user_agent。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`ua`| `str`| 必填| user_agent 信息  
  
**返回：**`None`

* * *

## ✅️️ `set.proxies()`[​](. "️️-setproxies的直接链接")

此方法用于设置代理 ip。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`http`| `str`| `None`| http 代理地址  
`https`| `str`| `None`| https 代理地址  
  
**返回：**`None`

* * *

## ✅️️ `set.auth()`[​](. "️️-setauth的直接链接")

此方法用于设置认证元组或对象。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`auth`| `Tuple[str, str]`  
`HTTPBasicAuth`| 必填| 认证元组或对象  
  
**返回：**`None`

* * *

## ✅️️ `set.hooks()`[​](. "️️-sethooks的直接链接")

此方法用于设置回调方法。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`hooks`| `dict`| 必填| 回调方法  
  
**返回：**`None`

* * *

## ✅️️ `set.params()`[​](. "️️-setparams的直接链接")

此方法用于设置查询参数字典。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`params`| `dict`| 必填| 查询参数字典  
  
**返回：**`None`

* * *

## ✅️️ `set.verify()`[​](. "️️-setverify的直接链接")

此方法用于设置是否验证SSL证书。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`on_off`| `bool`| 必填| `bool`表示开或关  
  
**返回：**`None`

* * *

## ✅️️ `set.cert()`[​](. "️️-setcert的直接链接")

此方法用于设置SSL客户端证书。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`cert`| `str`  
`Tuple[str, str]`| 必填| SSL客户端证书文件的路径(.pem格式)，或(‘cert’, ‘key’)元组  
  
**返回：**`None`

* * *

## ✅️️ `set.stream()`[​](. "️️-setstream的直接链接")

此方法用于设置是否使用流式响应内容。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`on_off`| `bool`| 必填| `bool`表示开或关  
  
**返回：**`None`

* * *

## ✅️️ `set.trust_env()`[​](. "️️-settrust_env的直接链接")

此方法用于设置是否信任环境。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`on_off`| `bool`| 必填| `bool`表示开或关  
  
**返回：**`None`

* * *

## ✅️️ `set.max_redirects()`[​](. "️️-setmax_redirects的直接链接")

此方法用于设置连接最大重定向次数。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
``times| `int`| 必填| 最大重定向次数  
  
**返回：**`None`

* * *

## ✅️️ `set.add_adapter()`[​](. "️️-setadd_adapter的直接链接")

此方法用于添加适配器。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`url`| `str`| 必填| 适配器对应url  
`adapter`| `HTTPAdapter`| 必填| 适配器对象  
  
**返回：**`None`

* * *

## ✅️️ `close()`[​](. "️️-close的直接链接")

此方法用于关闭连接。

**参数：** 无

**返回：**`None`

* * *

[上一页🛩️ 获取元素信息](get_ele_info.html)[下一页🛩️ 启动配置](session_opt.html)

  * [✅️️ `set.retry_times()`](.)
  * [✅️️ `set.retry_interval()`](.)
  * [✅️️ `set.timeout()`](.)
  * [✅️️ `set.encoding()`](.)
  * [✅️️ `set.cookies()`](.)
  * [✅️️ `set.cookies.clear()`](.)
  * [✅️️ `set.cookies.remove()`](.)
  * [✅️️ `set.headers()`](.)
  * [✅️️ `set.header()`](.)
  * [✅️️ `set.user_agent()`](.)
  * [✅️️ `set.proxies()`](.)
  * [✅️️ `set.auth()`](.)
  * [✅️️ `set.hooks()`](.)
  * [✅️️ `set.params()`](.)
  * [✅️️ `set.verify()`](.)
  * [✅️️ `set.cert()`](.)
  * [✅️️ `set.stream()`](.)
  * [✅️️ `set.trust_env()`](.)
  * [✅️️ `set.max_redirects()`](.)
  * [✅️️ `set.add_adapter()`](.)
  * [✅️️ `close()`](.)