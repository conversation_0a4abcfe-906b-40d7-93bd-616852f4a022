# 获取网页信息

> **来源文件**: `browser_control\get_page_info.html`  
> **转换时间**: 2025/08/14 周四  

---

* [](../.html)
  * 🚀 控制浏览器
  * 🛰️ 获取网页信息

本页总览

# 🛰️ 获取网页信息

[](https://b23.tv/w62Tiqd)

成功访问网页后，可使用 Tab 对象属性和方法获取页面信息。

## ✅️️ 页面信息[​](. "✅️️ 页面信息的直接链接")

### 📌 `html`[​](. "-html的直接链接")

此属性返回当前页面 html 文本。

注意

html 文本不包含`<iframe>`元素内容。

**返回类型：**`str`

* * *

### 📌 `json`[​](. "-json的直接链接")

此属性把请求内容解析成 json。

假如用浏览器访问会返回 `*.json` 文件的 url，浏览器会把 json 数据显示出来，这个参数可以把这些数据转换为`dict`格式。

**返回类型：**`dict`

* * *

### 📌 `title`[​](. "-title的直接链接")

此属性返回当前页面`title`文本。

**返回类型：**`str`

* * *

### 📌 `user_agent`[​](. "-user_agent的直接链接")

此属性返回当前页面 user agent 信息。

**返回类型：**`str`

* * *

### 📌 `save()`[​](. "-save的直接链接")

把当前页面保存为文件，同时返回保存的内容。

如果`path`和`name`参数都为`None`，只返回内容，不保存文件。

Page 对象和 Tab 对象有这个方法。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`path`| `str`  
`Path`| `None`| 保存路径，为`None`且`name`不为`None`时保存到当前路径  
`name`| `str`| `None`| 保存的文件名，为`None`且`path`不为`None`时使用 title 值  
`as_pdf`| `bool`| `False`| 为`Ture`保存为 pdf，否则保存为 mhtml 且忽略`kwargs`参数  
`**kwargs`| 多种| 无| pdf 生成参数  
  
pdf 生成参数包括：`landscape`, `displayHeaderFooter`, `printBackground`, `scale`, `paperWidth`, `paperHeight`, `marginTop`, `marginBottom`, `marginLeft`, `marginRight`, `pageRanges`, `headerTemplate`, `footerTemplate`, `preferCSSPageSize`, `generateTaggedPDF`, `generateDocumentOutline`

返回类型| 说明  
---|---  
`str`| `as_pdf`为`False`时返回 mhtml 文本  
`bytes`| `as_pdf`为`True`时返回文件字节数据  
  
* * *

## ✅️️ 运行状态信息[​](. "✅️️ 运行状态信息的直接链接")

### 📌 `url`[​](. "-url的直接链接")

此属性返回当前访问的 url。

**返回类型：**`str`

* * *

### 📌 `tab_id`[​](. "-tab_id的直接链接")

**返回类型：**`str`

此属性返回当前标签页的 id。

* * *

### 📌 `states.is_loading`[​](. "-statesis_loading的直接链接")

此属性返回页面是否正在加载状态。

**返回类型：**`bool`

* * *

### 📌 `states.is_alive`[​](. "-statesis_alive的直接链接")

此属性返回页面是否仍然可用，标签页已关闭则返回`False`。

**返回类型：**`bool`

* * *

### 📌 `states.ready_state`[​](. "-statesready_state的直接链接")

此属性返回页面当前加载状态，有 4 种：

  * `'connecting'`： 网页连接中
  * `'loading'`：表示文档还在加载中
  * `'interactive'`：DOM 已加载，但资源未加载完成
  * `'complete'`：所有内容已完成加载

**返回类型：**`str`

* * *

### 📌 `url_available`[​](. "-url_available的直接链接")

此属性以布尔值返回当前链接是否可用。

**返回类型：**`bool`

* * *

### 📌 `states.has_alert`[​](. "-stateshas_alert的直接链接")

此属性以布尔值返回页面是否存在弹出框。

**返回类型：**`bool`

* * *

## ✅️️ 窗口信息[​](. "✅️️ 窗口信息的直接链接")

### 📌 `rect.size`[​](. "-rectsize的直接链接")

以`tuple`返回页面大小，格式：(宽, 高)。

**返回类型：**`Tuple[int, int]`

* * *

### 📌 `rect.window_size`[​](. "-rectwindow_size的直接链接")

此属性以`tuple`返回窗口大小，格式：(宽, 高)。

**返回类型：**`Tuple[int, int]`

* * *

### 📌 `rect.window_location`[​](. "-rectwindow_location的直接链接")

此属性以`tuple`返回窗口在屏幕上的坐标，左上角为(0, 0)。

**返回类型：**`Tuple[int, int]`

* * *

### 📌 `rect.window_state`[​](. "-rectwindow_state的直接链接")

此属性以返回窗口当前状态，有`'normal'`、`'fullscreen'`、`'maximized'`、 `'minimized'`几种。

**返回类型：**`str`

* * *

### 📌 `rect.viewport_size`[​](. "-rectviewport_size的直接链接")

此属性以`tuple`返回视口大小，不含滚动条，格式：(宽, 高)。

**返回类型：**`Tuple[int, int]`

* * *

### 📌 `rect.viewport_size_with_scrollbar`[​](. "-rectviewport_size_with_scrollbar的直接链接")

此属性以`tuple`返回浏览器窗口大小，含滚动条，格式：(宽, 高)。

**返回类型：**`Tuple[int, int]`

* * *

### 📌 `rect.page_location`[​](. "-rectpage_location的直接链接")

此属性以`tuple`返回页面左上角在屏幕中坐标，左上角为(0, 0)。

**返回类型：**`Tuple[int, int]`

* * *

### 📌 `rect.viewport_location`[​](. "-rectviewport_location的直接链接")

此属性以`tuple`返回视口在屏幕中坐标，左上角为(0, 0)。

**返回类型：**`Tuple[int, int]`

* * *

### 📌 `rect.scroll_position`[​](. "-rectscroll_position的直接链接")

此属性返回页面滚动条位置，格式：(x, y)。

**类型：**`Tuple[float, float]`

* * *

  


## ✅️️ 配置参数信息[​](. "✅️️ 配置参数信息的直接链接")

### 📌 `timeout`[​](. "-timeout的直接链接")

此属性为整体默认超时时间（秒），包括元素查找、点击、处理提示框、列表选择等需要用到超时设置的地方，都以这个数据为默认值。默认为`10`。

**返回类型：**`int`、`float`

* * *

### 📌 `timeouts`[​](. "-timeouts的直接链接")

此属性以字典方式返回三种超时时间（秒）。

  * `'base'`：与`timeout`属性是同一个值
  * `'page_load'`：用于等待页面加载
  * `'script'`：用于等待脚本执行

**返回类型：**`dict`
    
    
    print(tab.timeouts)
    

**输出：**
    
    
    {'base': 10, 'page_load': 30.0, 'script': 30.0}
    

* * *

### 📌 `retry_times`[​](. "-retry_times的直接链接")

此属性为网络连接失败时的重试次数，默认为`3`。

**返回类型：**`int`

* * *

### 📌 `retry_interval`[​](. "-retry_interval的直接链接")

此属性为网络连接失败时的重试等待间隔秒数，默认为`2`。

**返回类型：**`int`、`float`

* * *

### 📌 `load_mode`[​](. "-load_mode的直接链接")

此属性返回页面加载策略，有 3 种：

  * `'normal'`：等待页面所有资源完成加载
  * `'eager'`：DOM 加载完成即停止
  * `'none'`：页面完成连接即停止

**返回类型：**`str`

* * *

## ✅️️ cookies 和缓存信息[​](. "✅️️ cookies 和缓存信息的直接链接")

### 📌 `cookies()`[​](. "-cookies的直接链接")

此方法以列表方式返回 cookies 信息。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`all_domains`| `bool`| `False`| 是否返回所有 cookies，为`False`只返回当前 url 的  
`all_info`| `bool`| `False`| 返回的 cookies 是否包含所有信息，`False`时只包含`name`、`value`、`domain`信息  
返回类型| 说明  
---|---  
`CookiesList`| cookies 组成的列表  
  
**示例：**
    
    
    from DrissionPage import Chromium
    
    tab = Chromium().latest_tab
    tab.get('https://www.baidu.com')
    
    for i in tab.cookies():
        print(i)
    

**输出：**
    
    
    {'domain': '.baidu.com', 'domain_specified': True, ......}
    ......
    

* * *

### 📌 指定返回类型[​](. "📌 指定返回类型的直接链接")

`cookies()`方法返回的列表可转换为其它指定格式。

  * `cookies().as_str()`：`'name1=value1; name2=value2'`格式的字符串
  * `cookies().as_dict()`：`{name1: value1, name2: value2}`格式的字典
  * `cookies().as_json()`：json 格式的字符串

说明

`as_str()`和`as_dict()`都只会保留`'name'`和`'value'`字段。

* * *

### 📌 `session_storage()`[​](. "-session_storage的直接链接")

此方法用于获取 sessionStorage 信息，可获取全部或单个项。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`item`| `str`| `None`| 要获取的项目，为`None`则返回全部项目组成的字典  
返回类型| 说明  
---|---  
`dict`| `item`参数为`None`时返回所有项目  
`str`| 指定`item`时返回该项目内容  
  
* * *

### 📌 `local_storage()`[​](. "-local_storage的直接链接")

此方法用于获取 localStorage 信息，可获取全部或单个项。

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`item`| `str`| `None`| 要获取的项目，为`None`则返回全部项目组成的字典  
返回类型| 说明  
---|---  
`dict`| `item`参数为`None`时返回所有项目  
`str`| 指定`item`时返回该项目内容  
  
* * *

[上一页🛰️ 页面交互](page_operation.html)[下一页🔦 概述](get_elements/intro.html)

  * [✅️️ 页面信息](.)
    * [📌 `html`](.)
    * [📌 `json`](.)
    * [📌 `title`](.)
    * [📌 `user_agent`](.)
    * [📌 `save()`](.)
  * [✅️️ 运行状态信息](.)
    * [📌 `url`](.)
    * [📌 `tab_id`](.)
    * [📌 `states.is_loading`](.)
    * [📌 `states.is_alive`](.)
    * [📌 `states.ready_state`](.)
    * [📌 `url_available`](.)
    * [📌 `states.has_alert`](.)
  * [✅️️ 窗口信息](.)
    * [📌 `rect.size`](.)
    * [📌 `rect.window_size`](.)
    * [📌 `rect.window_location`](.)
    * [📌 `rect.window_state`](.)
    * [📌 `rect.viewport_size`](.)
    * [📌 `rect.viewport_size_with_scrollbar`](.)
    * [📌 `rect.page_location`](.)
    * [📌 `rect.viewport_location`](.)
    * [📌 `rect.scroll_position`](.)
  * [✅️️ 配置参数信息](.)
    * [📌 `timeout`](.)
    * [📌 `timeouts`](.)
    * [📌 `retry_times`](.)
    * [📌 `retry_interval`](.)
    * [📌 `load_mode`](.)
  * [✅️️ cookies 和缓存信息](.)
    * [📌 `cookies()`](.)
    * [📌 指定返回类型](.)
    * [📌 `session_storage()`](.)
    * [📌 `local_storage()`](.)