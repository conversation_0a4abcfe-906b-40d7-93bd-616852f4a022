# 概述

> **来源文件**: `SessionPage\intro.html`  
> **转换时间**: 2025/08/14 周四  

---

* [](../.html)
  * 🛫 SessionPage
  * 🛩️ 概述

# 🛩️ 概述

[](https://b23.tv/w62Tiqd)

`SessionPage`对象和`WebPage`对象的 s 模式，可用收发数据包的形式访问网页。

`SessionPage`是一个使用使用`Session`（requests 库）对象的页面，封装了网络连接和结果解析功能，使收发数据包也可以像操作页面一样便利。

**示例：**

获取 gitee 推荐项目第一页所有项目。
    
    
    # 导入
    from DrissionPage import SessionPage
    # 创建页面对象
    page = SessionPage()
    # 访问网页
    page.get('https://gitee.com/explore/all')
    # 在页面中查找元素
    items = page.eles('t:h3')
    # 遍历元素
    for item in items[:-1]:
        # 获取当前<h3>元素下的<a>元素
        lnk = item('tag:a')
        # 打印<a>元素文本和href属性
        print(lnk.text, lnk.link)
    

**输出：**
    
    
    七年觐汐/wx-calendar https://gitee.com/qq_connect-EC6BCC0B556624342/wx-calendar
    ThingsPanel/thingspanel-go https://gitee.com/ThingsPanel/thingspanel-go
    APITable/APITable https://gitee.com/apitable/APITable
    Indexea/ideaseg https://gitee.com/indexea/ideaseg
    CcSimple/vue-plugin-hiprint https://gitee.com/CcSimple/vue-plugin-hiprint
    william_lzw/ExDUIR.NET https://gitee.com/william_lzw/ExDUIR.NET
    anolis/ancert https://gitee.com/anolis/ancert
    cozodb/cozo https://gitee.com/cozodb/cozo
    后面省略...
    

  


[上一页🛰️ Page 对象](../browser_control/pages.html)[下一页🛩️ 创建页面对象](create_obj.html)