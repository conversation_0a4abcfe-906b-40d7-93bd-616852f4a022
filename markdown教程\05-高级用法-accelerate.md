# 数据读取加速

> **来源文件**: `advance\accelerate.html`  
> **转换时间**: 2025/08/14 周四  

---

* [](../.html)
  * 🧰 进阶使用
  * ⚙️ 数据读取加速

本页总览

# ⚙️ 数据读取加速

[](https://b23.tv/w62Tiqd)

本节演示一个能够大幅加快浏览器页面数据采集的黑科技。

## ✅️️ 示例[​](. "✅️️ 示例的直接链接")

我们找一个比较大的页面来演示，比如网页首页：[https://www.163.com](https://www.163.com/)

我们数一下这个网页内的`<a>`元素数量：
    
    
    from DrissionPage import Chromium
    
    tab = Chromium().latest_tab
    tab.get('https://www.163.com')
    print(len(tab('t:body').eles('t:a')))
    

**输出：**
    
    
    1613
    

嗯，数量不少，可以看出效果。

假如现在我们的任务是打印所有链接的文本，常规做法是遍历所有元素，然后打印。

这里引入本库作者写的一个计时工具，可以标记一段代码运行时间。您也可以用其它方法计时。
    
    
    from DrissionPage import Chromium
    from TimePinner import Pinner  # 导入计时工具
    
    pinner = Pinner()  # 创建计时器对象
    tab = Chromium().latest_tab
    tab.get('https://www.163.com')
    
    pinner.pin()  # 标记开始记录
    
    # 获取所有链接对象并遍历
    links = tab('t:body').eles('t:a')
    for lnk in links:
        print(lnk.text)
    
    pinner.pin('用时')  # 记录并打印时间
    

**输出：**
    
    
    0.0
    
    网络大过年_网易政务_网易网
    网易首页
    ...中间省略...
    不良信息举报 Complaint Center
    廉正举报
    用时：4.057772700001806
    

用时 4 秒。

现在，我们稍微修改一个小小的地方。

把`page('t:body').eles('t:a')`改成`page('t:body').s_eles('t:a')`，然后再执行一次。
    
    
    from DrissionPage import Chromium
    from TimePinner import Pinner  # 导入计时工具
    
    pinner = Pinner()  # 创建计时器对象
    tab = Chromium().latest_tab
    tab.get('https://www.163.com')
    
    pinner.pin()  # 标记开始记录
    
    # 获取所有链接对象并遍历
    links = tab('t:body').s_eles('t:a')
    for lnk in links:
        print(lnk.text)
    
    pinner.pin('用时')  # 记录并打印时间
    

**输出：**
    
    
    0.0
    
    网络大过年_网易政务_网易网
    网易首页
    ...中间省略...
    不良信息举报 Complaint Center
    廉正举报
    用时：0.2797656000002462
    

神奇不？原来 4 秒的采集时间现在只需 0.28 秒。

* * *

## ✅️️ 解读[​](. "✅️️ 解读的直接链接")

`s_eles()`与`eles()`的区别在于前者会把整个页面或动态元素转变成一个静态元素，再在其中获取下级元素或信息。

因为静态元素是纯文本的，没有各种属性、交互等消耗资源的部分，所以运行速度非常快。

作者曾经采集过一个非常复杂的页面，动态元素用时 30 秒，转静态元素就只要 0.X 秒，加速效果非常明显。

我们可以获取页面中内容容器（示例中的`<body>`），把它转换成静态元素，再在其中获取信息。

当然，静态元素没有交互功能，它只是副本，也不会影响原来的动态元素。

说明

一个页面中不用反复使用`s_ele()`，通常只要使用一次，获取最高级的容器元素或者页面对象本身的静态副本，然后在这个副本中查找元素。 反复使用的话会因为资源消耗较大导致不稳定和浪费时间。

  


[上一页⚙️ 异常的使用](errors.html)[下一页⚙️ 打包程序](packaging.html)

  * [✅️️ 示例](.)
  * [✅️️ 解读](.)