#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DrissionPage API监控示例：专门监控指定API接口
功能：新建标签页、监控指定API、显示完整响应信息
目标API：https://agentseller.temu.com/visage-agent-seller/product/skc/pageQuery
"""

from DrissionPage import Chromium
import json
import time

def monitor_specific_api():
    """
    监控指定API接口的完整示例
    专门监控 pageQuery API 并显示完整响应信息
    """
    print("🚀 开始新建标签页并监控指定API...")
    
    # 连接到9222端口的浏览器（如果没有会自动启动）
    browser = Chromium(9222)
    print("✅ 已连接到浏览器")
    
    # 新建一个空白标签页（不立即访问网址）
    new_tab = browser.new_tab()
    print("📄 新建空白标签页完成")
    
    # 激活新标签页（显示在前台）
    new_tab.set.activate()
    print("🎯 标签页已激活")
    
    # 启动网络监听器 - 只监听指定的API接口
    target_api = "visage-agent-seller/product/skc/pageQuery"
    print("🔍 启动网络监听器...")
    print(f"🎯 目标API: {target_api}")
    new_tab.listen.start(targets=target_api)  # 只监听包含此路径的请求
    print("✅ 网络监听器已启动，开始监控指定API接口")

    # 访问目标网址
    target_url = "https://agentseller.temu.com/goods/list"
    print(f"🌐 开始访问目标网址：{target_url}")

    # 记录开始时间
    start_time = time.time()

    # 访问网址
    new_tab.get(target_url)

    # 立即尝试捕获API请求
    print("⏳ 立即检查是否有API请求...")
    try:
        # 先尝试短时间等待，看是否有立即的API请求
        packet = new_tab.listen.wait(timeout=5)
        if packet:
            print("✅ 在页面加载过程中捕获到API请求！")
            return new_tab, packet
    except:
        pass
    
    print("⏳ 等待页面开始加载...")
    new_tab.wait.load_start()  # 等待页面开始加载
    print("✅ 页面开始加载")
    
    print("⏳ 等待文档加载完成...")
    new_tab.wait.doc_loaded()  # 等待文档加载完成
    print("✅ 文档加载完成")
    
    # 计算加载时间
    load_time = time.time() - start_time
    print(f"⏱️ 页面基础加载时间：{load_time:.2f}秒")

    return new_tab, None

def capture_api_response(tab):
    """
    捕获并显示API响应的完整信息
    """
    print("\n" + "=" * 80)
    print("📡 开始监控API请求...")
    print("=" * 80)
    
    # 等待API请求
    print("⏳ 等待目标API请求...")
    print("💡 提示：如果页面已经加载完成但没有API请求，请尝试刷新页面或进行操作")
    
    try:
        # 等待指定的API请求，设置较长的超时时间
        packet = tab.listen.wait(timeout=60)  # 等待60秒
        if not packet:
            print("⚠️ 未捕获到目标API请求")
            print("💡 可能的原因：")
            print("   1. API请求已经在监听启动前完成")
            print("   2. 页面没有发起该API请求")
            print("   3. API路径发生了变化")
            return None
    except Exception as e:
        print(f"❌ 等待API请求时发生错误：{e}")
        return None
    
    print(f"✅ 成功捕获到API请求！")
    
    # 显示API请求的完整信息
    print("\n" + "=" * 80)
    print("🎯 API请求完整信息")
    print("=" * 80)
    
    # 显示API请求的基本信息
    print(f"🔗 请求URL: {packet.url}")
    print(f"📋 请求方法: {packet.method}")
    print(f"📂 资源类型: {packet.resourceType}")
    print(f"❌ 是否失败: {packet.is_failed}")
    
    # 显示请求信息
    if packet.request:
        print(f"\n📤 请求详细信息:")
        print(f"   完整URL: {packet.request.url}")
        
        # 显示所有请求头
        if packet.request.headers:
            print(f"\n   📋 请求头信息 (共{len(packet.request.headers)}个):")
            for key, value in packet.request.headers.items():
                print(f"      {key}: {value}")
        
        # 显示POST数据
        if packet.request.postData:
            print(f"\n   📝 POST数据:")
            try:
                # 尝试格式化JSON数据
                if isinstance(packet.request.postData, dict):
                    formatted_data = json.dumps(packet.request.postData, indent=4, ensure_ascii=False)
                    print(f"{formatted_data}")
                else:
                    # 尝试解析字符串为JSON
                    try:
                        parsed_data = json.loads(str(packet.request.postData))
                        formatted_data = json.dumps(parsed_data, indent=4, ensure_ascii=False)
                        print(f"{formatted_data}")
                    except:
                        print(f"      {packet.request.postData}")
            except Exception as e:
                print(f"      解析POST数据出错: {e}")
                print(f"      原始POST数据: {packet.request.postData}")
    
    # 显示响应信息 - 这是重点！
    if packet.response:
        print(f"\n📥 响应详细信息:")
        print(f"   状态码: {packet.response.status}")
        print(f"   状态文本: {packet.response.statusText}")
        
        # 显示所有响应头
        if packet.response.headers:
            print(f"\n   📋 响应头信息 (共{len(packet.response.headers)}个):")
            for key, value in packet.response.headers.items():
                print(f"      {key}: {value}")
        
        # 显示完整响应体 - 这是最重要的部分！
        if hasattr(packet.response, 'body') and packet.response.body:
            print(f"\n   📄 完整响应体:")
            print("   " + "=" * 70)
            try:
                # 如果是JSON格式，进行美化输出
                if isinstance(packet.response.body, dict):
                    formatted_response = json.dumps(packet.response.body, indent=4, ensure_ascii=False)
                    print(formatted_response)
                else:
                    # 尝试解析为JSON
                    try:
                        parsed_json = json.loads(str(packet.response.body))
                        formatted_response = json.dumps(parsed_json, indent=4, ensure_ascii=False)
                        print(formatted_response)
                    except:
                        # 如果不是JSON，直接输出
                        response_str = str(packet.response.body)
                        print(response_str)
            except Exception as e:
                print(f"   ⚠️ 响应体解析出错: {e}")
                print(f"   原始响应体: {packet.response.body}")
            print("   " + "=" * 70)
        else:
            print(f"\n   ⚠️ 响应体为空或无法获取")
    else:
        print(f"\n   ⚠️ 无响应信息")
    
    # 显示失败信息（如果有）
    if packet.is_failed and packet.fail_info:
        print(f"\n❌ 请求失败信息:")
        print(f"   错误文本: {packet.fail_info.errorText}")
        print(f"   是否取消: {packet.fail_info.canceled}")
        if packet.fail_info.blockedReason:
            print(f"   拦截原因: {packet.fail_info.blockedReason}")
    
    return packet

if __name__ == "__main__":
    """
    主程序：执行API监控操作
    """
    print("=" * 80)
    print("📚 DrissionPage API监控学习示例")
    print("🎯 专门监控: visage-agent-seller/product/skc/pageQuery")
    print("=" * 80)
    
    try:
        # 步骤1：新建标签页并启动监控
        print("🚀 第一步：新建标签页并启动API监控")
        result = monitor_specific_api()

        if result[0]:  # tab存在
            tab = result[0]
            early_packet = result[1]  # 可能在页面加载时就捕获到的包

            print("\n✅ 标签页创建和API监控启动完成！")
            print(f"📄 当前页面标题：{tab.title}")
            print(f"🔗 当前页面URL：{tab.url}")

            # 如果已经捕获到包，直接显示
            if early_packet:
                print("\n🎉 在页面加载时就捕获到了API请求！")
                packet = early_packet
            else:
                # 步骤2：继续等待捕获API响应
                print("\n🚀 第二步：继续等待API响应")
                packet = capture_api_response(tab)
            
            if packet:
                print("\n🎉 API监控完成！")
                print("📋 操作总结：")
                print(f"   ✅ 成功监控到目标API请求")
                print(f"   ✅ 显示了完整的请求和响应信息")
                print(f"   ✅ API状态码: {packet.response.status if packet.response else '无响应'}")
                
                # 停止监听器
                tab.listen.stop()
                print("   ✅ 网络监听器已停止")
            else:
                print("\n⚠️ 未能捕获到目标API请求")
                print("💡 建议：")
                print("   1. 检查API路径是否正确")
                print("   2. 尝试在页面上进行操作以触发API请求")
                print("   3. 检查页面是否需要登录")
        else:
            print("❌ 标签页创建失败")
        
    except Exception as e:
        print(f"❌ 程序执行过程中发生错误：{e}")
        print("💡 请检查：")
        print("   1. 浏览器是否正确安装并可以启动")
        print("   2. 网络连接是否正常")
        print("   3. 目标网址是否可访问")
        print("   4. DrissionPage库是否正确安装")
    
    print("\n" + "=" * 80)
    print("📚 DrissionPage API监控学习完成")
    print("💡 如需监控其他API，请修改target_api变量")
    print("=" * 80)
