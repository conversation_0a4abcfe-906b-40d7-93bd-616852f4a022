# 获取页面信息

> **来源文件**: `SessionPage\get_page_info.html`  
> **转换时间**: 2025/08/14 周四  

---

* [](../.html)
  * 🛫 SessionPage
  * 🛩️ 获取页面信息

本页总览

# 🛩️ 获取页面信息

[](https://b23.tv/w62Tiqd)

成功访问网页后，可使用`SessionPage`对象自身属性和方法获取页面信息。
    
    
    from DrissionPage import SessionPage
    
    page = SessionPage()
    page.get('http://www.baidu.com')
    # 获取页面标题
    print(page.title)
    # 获取页面html
    print(page.html)
    

**输出：**
    
    
    百度一下，你就知道
    <!DOCTYPE html>
    <html> <head><meta http-equi...
    

* * *

## ✅️️ 页面信息[​](. "✅️️ 页面信息的直接链接")

### 📌 `url`[​](. "-url的直接链接")

此属性返回当前访问的 url。

**类型：**`str`

* * *

### 📌 `url_available`[​](. "-url_available的直接链接")

此属性以布尔值返回当前链接是否可用。

**类型：**`bool`

* * *

### 📌 `title`[​](. "-title的直接链接")

此属性返回当前页面`title`文本。

**类型：**`str`

* * *

### 📌 `raw_data`[​](. "-raw_data的直接链接")

此属性返回访问到的元素数据，即`Response`对象的`content`属性。

**类型：**`bytes`

* * *

### 📌 `html`[​](. "-html的直接链接")

此属性返回当前页面 html 文本。

**类型：**`str`

* * *

### 📌 `json`[​](. "-json的直接链接")

此属性把返回内容解析成 json。  
比如请求接口时，若返回内容是 json 格式，用`html`属性获取的话会得到一个字符串，用此属性获取可将其解析成`dict`。 支持访问 `*.json` 文件，也支持 API 返回的json字符串。

**类型：**`dict`

* * *

### 📌 `user_agent`[​](. "-user_agent的直接链接")

此属性返回当前页面 user_agent 信息。

**类型：**`str`

* * *

## ✅️️ 运行参数信息[​](. "✅️️ 运行参数信息的直接链接")

### 📌 `timeout`[​](. "-timeout的直接链接")

此属性返回网络请求超时时间，默认为 10 秒。

**类型：**`int`、`float`

* * *

### 📌 `retry_times`[​](. "-retry_times的直接链接")

此属性为网络连接失败时的重试次数，默认为`3`。

**类型：**`int`

* * *

### 📌 `retry_interval`[​](. "-retry_interval的直接链接")

此属性为网络连接失败时的重试等待间隔秒数，默认为`2`。

**类型：**`int`、`float`

* * *

### 📌 `encoding`[​](. "-encoding的直接链接")

此属性返回用户主动设置的编码格式。

* * *

  


## ✅️️ cookies 信息[​](. "✅️️ cookies 信息的直接链接")

### 📌 `cookies()`[​](. "-cookies的直接链接")

此方法返回 cookies 信息。

**类型：**`dict`、`list`

参数名称| 类型| 默认值| 说明  
---|---|---|---  
`all_domains`| `bool`| `False`| 是否返回所有 cookies，为`False`只返回当前 url 的  
`all_info`| `bool`| `False`| 返回的 cookies 是否包含所有信息，`False`时只包含`name`、`value`、`domain`信息  
返回类型| 说明  
---|---  
`CookiesList`| cookies 组成的列表  
  
`cookies()`方法返回的列表可转换为其它指定格式。

  * `cookies().as_str()`：`'name1=value1; name2=value2'`格式的字符串
  * `cookies().as_dict()`：`{name1: value1, name2: value2}`格式的字典
  * `cookies().as_json()`：json 格式的字符串

说明

`as_str()`和`as_dict()`都只会保留`'name'`和`'value'`字段。

**示例：**
    
    
    from DrissionPage import SessionPage
    
    page = SessionPage()
    page.get('http://www.baidu.com')
    page.get('http://gitee.com')
    
    for i in page.cookies(all_domains=True):
        print(i)
    

**输出：**
    
    
    {'name': 'BDORZ', 'value': '27875', 'domain': '.baidu.com'}
    {'name': 'BEC', 'value': '1f1759dfh65j65j5j4feb0357', 'domain': 'gitee.com'}
    

* * *

## ✅️️ 内嵌对象[​](. "✅️️ 内嵌对象的直接链接")

### 📌 `session`[​](. "-session的直接链接")

此属性返回当前页面对象使用的`Session`对象。

**类型：**`Session`

* * *

### 📌 `response`[​](. "-response的直接链接")

此属性为请求网页后生成的`Response`对象，本库没实现的功能可直接获取此属性调用 requests 库的原生功能。

**类型：**`Response`
    
    
    # 打印连接状态
    r = page.response
    print(r.status_code)
    

[上一页🛩️ 访问网页](visit.html)[下一页🛩️ 查找元素](get_ele.html)

  * [✅️️ 页面信息](.)
    * [📌 `url`](.)
    * [📌 `url_available`](.)
    * [📌 `title`](.)
    * [📌 `raw_data`](.)
    * [📌 `html`](.)
    * [📌 `json`](.)
    * [📌 `user_agent`](.)
  * [✅️️ 运行参数信息](.)
    * [📌 `timeout`](.)
    * [📌 `retry_times`](.)
    * [📌 `retry_interval`](.)
    * [📌 `encoding`](.)
  * [✅️️ cookies 信息](.)
    * [📌 `cookies()`](.)
  * [✅️️ 内嵌对象](.)
    * [📌 `session`](.)
    * [📌 `response`](.)