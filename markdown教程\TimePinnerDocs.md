# 欢迎

> **来源文件**: `TimePinnerDocs.html`  
> **转换时间**: 2025/08/14 周四  

---

[ ](.html "TimePinner") TimePinner 

[ Gitee  ](https://gitee.com/g1879/TimePinner "Go to repository")

  * 欢迎  [ 欢迎  ](.html) Table of contents 
    * [ 💬 简介 ](.)
    * [ 💥 简单示例 ](.)
      * [ 📌 时间点间的间隔 ](.)
      * [ 📌 时间点与起始点的间隔 ](.)
    * [ ☕ 请我喝咖啡 ](.)
  * [ 安装和导入  ](https://drissionpage.cn/install_and_import/)
  * [ 使用方法  ](https://drissionpage.cn/usage/)
  * [ APIs  ](https://drissionpage.cn/APIs/)
  * [ 更多作品  ](https://drissionpage.cn/works/)

Table of contents 

  * [ 💬 简介 ](.)
  * [ 💥 简单示例 ](.)
    * [ 📌 时间点间的间隔 ](.)
    * [ 📌 时间点与起始点的间隔 ](.)
  * [ ☕ 请我喝咖啡 ](.)

# 欢迎

## 💬 简介

TimePinner 是一个简单的计时工具。

类似于代码中的秒表。

可标记多个点，以记录若干段时间长度。

每段时间可以命名，以方便记忆，也可跳过无须记录的时间段。

当前版本：v0.2.0

* * *

## 💥 简单示例

### 📌 时间点间的间隔
    
    
    [](.)from TimePinner import Pinner
    [](.)from time import sleep
    [](.)
    [](.)p = Pinner(pin=True)  # 创建对象，同时开始计时
    [](.)sleep(2)
    [](.)p.pin('第一段')
    [](.)sleep(3)
    [](.)p.pin('第二段')
    

输出 
    
    
    [](.)起始点：0.0
    [](.)第一段：2.000334299984388
    [](.)第二段：3.0005646001081914
    

### 📌 时间点与起始点的间隔

适用于多线程记录。
    
    
    [](.)from TimePinner import Pinner
    [](.)from threading import Thread
    [](.)from random import randint
    [](.)from time import sleep
    [](.)
    [](.)
    [](.)def do_something(pinner: Pinner, num):
    [](.)    sleep(randint(1, 10))
    [](.)    pinner.pin(num, all_time=True)
    [](.)
    [](.)
    [](.)p = Pinner(pin=True, show_everytime=False)
    [](.)t1 = Thread(target=do_something, args=(p, '线程1'))
    [](.)t2 = Thread(target=do_something, args=(p, '线程2'))
    [](.)t1.start()
    [](.)t2.start()
    [](.)t1.join()
    [](.)t2.join()
    [](.)
    [](.)p.show(all_time=True)
    

输出 
    
    
    [](.)线程1：3.0009817000245675
    [](.)线程2：5.001444000052288
    

* * *

## ☕ 请我喝咖啡

如果本项目对您有所帮助，不妨请作者我喝杯咖啡 ：）

Back to top