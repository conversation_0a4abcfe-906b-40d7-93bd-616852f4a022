# Drissionrecord

> **来源文件**: `DrissionRecord.html`  
> **转换时间**: 2025/08/14 周四  

---

本库是一个基于 python 的工具集，用于记录数据到文件。

使用方便，代码简洁，是一个可靠、省心且实用的工具。

前身是作者旧作 [DataRecorder](https://drissionpage.cn/DataRecorderDocs/)，在此基础上进行了大量重构和优化。

当前版本：1.0.0

**联系邮箱：** [<EMAIL>](<EMAIL>)

  


* * *

## ✨️ 理念[​](. "✨️ 理念的直接链接")

简单，可靠，省心。

* * *

## 📕 背景[​](. "📕 背景的直接链接")

进行数据采集的时候，常常要保存数据到文件，频繁开关文件会影响效率，而如果等采集结束再写入，会有因异常而丢失数据的风险。

因此写了这些工具，只要把数据扔进去，它们能缓存到一定数量再一次写入，减少文件开关次数，且在程序崩溃或退出时尽量自动保存。

它们使用非常方便，无论何时何地，无论什么格式，只要使用`add_data()`方法把数据存进去即可，语法极其简明扼要，使程序员能更专注业务逻辑。

它们还相当可靠，作者曾一次过连续记录超过 300 万条数据，也曾 50 个线程同时运行写入数万条数据到一个文件，依然轻松胜任。

工具还对表格文件（xlsx、csv）做了很多优化，封装了实用功能，可以使用表格文件方便地实现断点续爬、批量转移数据、指定坐标填写数据等。

* * *

## 🍀 特性[​](. "🍀 特性的直接链接")

  * 可缓存大量数据一次写入，减少文件读写次数，降低开销
  * 支持多线程同时写入数据
  * 自动创建文件和路径，减少代码量
  * 自动匹配和创建表头
  * 可按条件读取数据
  * 可方便地批量转移数据
  * 灵活的单元格坐标处理

* * *

## ☕ 请我喝咖啡[​](. "☕ 请我喝咖啡的直接链接")

如果本项目对您有所帮助，不妨请作者我喝杯咖啡 ：）

  * [✨️ 理念](.)
  * [📕 背景](.)
  * [🍀 特性](.)
  * [☕ 请我喝咖啡](.)