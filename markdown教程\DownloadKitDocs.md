# 欢迎

> **来源文件**: `DownloadKitDocs.html`  
> **转换时间**: 2025/08/14 周四  

---

[ ](.html "DownloadKit") DownloadKit 

[ Gitee  ](https://gitee.com/g1879/DownloadKit "Go to repository")

  * 欢迎  [ 欢迎  ](.html) Table of contents 
    * [ 💬 简介 ](.)
    * [ 💖 特性 ](.)
    * [ 💥 简单示例 ](.)
    * [ ☕ 请我喝咖啡 ](.)
  * [ 安装和导入  ](https://drissionpage.cn/install_and_import/)
  * 使用方法  使用方法 
    * [ 🐘 创建对象  ](https://drissionpage.cn/usage/create_object/)
    * [ 🐎 添加任务  ](https://drissionpage.cn/usage/add_missions/)
    * [ 🐒 任务管理  ](https://drissionpage.cn/usage/misssions/)
    * [ 🦍 运行设置  ](https://drissionpage.cn/usage/settings/)
    * [ 🐇 APIs  ](https://drissionpage.cn/usage/APIs/)
  * [ 版本历史  ](https://drissionpage.cn/history/)
  * [ 更多作品  ](https://drissionpage.cn/works/)

Table of contents 

  * [ 💬 简介 ](.)
  * [ 💖 特性 ](.)
  * [ 💥 简单示例 ](.)
  * [ ☕ 请我喝咖啡 ](.)

# 欢迎

## 💬 简介

DownloadKit 是一个基于 python 的简洁易用的多线程文件下载工具。 使用简单，功能强大。

当前版本：v2.0.7

* * *

## 💖 特性

  * 多线程，可同时下载多个文件
  * 大文件自动分块用多线程下载
  * 自动任务调度，简易的任务添加方式
  * 可使用已有`Session`对象，便于保持登录状态
  * 与 DrissionPage 良好兼容
  * 自动创建目标路径
  * 自动去除路径中的非法字符
  * 自动处理文件名冲突
  * 可对现有文件追加内容
  * 连接失败自动重试

* * *

## 💥 简单示例
    
    
    [](.)from DownloadKit import DownloadKit
    [](.)
    [](.)# 创建下载器对象
    [](.)d = DownloadKit(r'.\files')
    [](.)
    [](.)# 添加多个任务
    [](.)url1 = 'https://gitee.com/static/images/logo.svg?t=158106664'
    [](.)url2 = 'https://www.baidu.com/img/PCfb_5bf082d29588c07f842ccde3f97243ea.png'
    [](.)
    [](.)d.download(url1)
    [](.)d.download(url2)
    

* * *

## ☕ 请我喝咖啡

如果本项目对您有所帮助，不妨请作者我喝杯咖啡 ：）

Back to top